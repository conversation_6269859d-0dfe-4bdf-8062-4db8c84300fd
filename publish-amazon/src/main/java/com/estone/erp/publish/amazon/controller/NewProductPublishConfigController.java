package com.estone.erp.publish.amazon.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.amazon.model.NewProductPublishConfig;
import com.estone.erp.publish.amazon.model.NewProductPublishConfigCriteria;
import com.estone.erp.publish.amazon.model.dto.NewProductPublishConfigVO;
import com.estone.erp.publish.amazon.service.NewProductPublishConfigService;
import com.estone.erp.publish.common.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 2024-05-29 17:54:40
 */
@Slf4j
@RestController
@RequestMapping("newProductPublishConfig")
public class NewProductPublishConfigController {
    @Resource
    private NewProductPublishConfigService newProductPublishConfigService;


    /**
     * 查询
     *
     * @param requestParam
     * @return
     */
    @PostMapping("/search")
    public ApiResult<?> postNewProductPublishConfig(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchNewProductPublishConfig": // 查询列表
                    CQuery<NewProductPublishConfigCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<NewProductPublishConfigCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<NewProductPublishConfigVO> results = newProductPublishConfigService.search(cquery);
                    return results;
                }
        }
        return ApiResult.newSuccess();
    }


    /**
     * 更新
     * <p>
     * [{
     * "id": 1,
     * "saleNumber": "123",
     * "allocatedQuantity": 10
     * }]
     *
     * @param requestParam
     * @return
     */
    @PostMapping(value = "/update")
    public ApiResult<?> updateNewProductPublishConfig(@RequestBody(required = true) List<NewProductPublishConfig> requestParam) {
        newProductPublishConfigService.updateNewProductPublishConfig(requestParam);
        return ApiResult.newSuccess();
    }


    /**
     * 添加
     * [{
     * "saleNumber": "123"
     * }]
     *
     * @param requestParam
     * @return
     */
    @PostMapping(value = "/add")
    public ApiResult<String> addNewProductPublishConfig(@RequestBody(required = true) List<NewProductPublishConfigVO> requestParam) {
        return newProductPublishConfigService.importSaleConfig(requestParam);
    }

    /**
     * 导入销售配置
     *
     * @return ApiResult
     */
    @PostMapping(value = "/import/saleConfig")
    public ApiResult<String> importSaleConfig(@RequestParam(value = "file", required = true) MultipartFile file) {
        try {
            List<NewProductPublishConfigVO> employeeList = new ArrayList<>();
            EasyExcel.read(file.getInputStream(), NewProductPublishConfigVO.class, new AnalysisEventListener<NewProductPublishConfigVO>() {
                @Override
                public void invoke(NewProductPublishConfigVO data, AnalysisContext context) {
                    employeeList.add(data);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("导入完成：{}", context.readRowHolder().getRowIndex());
                }
            }).sheet().doRead();
            return newProductPublishConfigService.importSaleConfig(employeeList);
        } catch (Exception e) {
            log.error("导入销售配置,异常：", e);
            return ApiResult.newError("导入销售配置,异常：" + e.getMessage());
        }
    }

}