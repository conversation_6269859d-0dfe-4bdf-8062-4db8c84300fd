package com.estone.erp.publish.amazon.service.impl;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.mapper.NewProductPublishConfigMapper;
import com.estone.erp.publish.amazon.model.NewProductPublishConfig;
import com.estone.erp.publish.amazon.model.NewProductPublishConfigCriteria;
import com.estone.erp.publish.amazon.model.NewProductPublishConfigExample;
import com.estone.erp.publish.amazon.model.dto.NewProductPublishConfigVO;
import com.estone.erp.publish.amazon.service.NewProductPublishConfigService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2024-05-29 17:54:40
 */
@Service("newProductPublishConfigService")
@Slf4j
public class NewProductPublishConfigServiceImpl implements NewProductPublishConfigService {
    @Resource
    private NewProductPublishConfigMapper newProductPublishConfigMapper;
    @Resource
    private PermissionsHelper permissionsHelper;

    @Override
    public CQueryResult<NewProductPublishConfigVO> search(CQuery<NewProductPublishConfigCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        NewProductPublishConfigCriteria query = cquery.getSearch();

        List<String> userEmployeeNoPermission = permissionsHelper.getCurrentUserEmployeeNoPermission(query.getSaleNumbers(), query.getSaleSupervisors(), query.getSaleLeaders(), SaleChannel.CHANNEL_AMAZON);
        query.setSaleNumbers(userEmployeeNoPermission);

        NewProductPublishConfigExample example = query.getExample();

        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = newProductPublishConfigMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<NewProductPublishConfig> newProductPublishConfigs = newProductPublishConfigMapper.selectByExample(example);


        if (CollectionUtils.isNotEmpty(newProductPublishConfigs)) {
            List<NewProductPublishConfigVO> newProductPublishConfigVOS = newProductPublishConfigs.stream().map(NewProductPublishConfigVO::convertToVO).collect(Collectors.toList());
            return new CQueryResult<>(total, totalPages, newProductPublishConfigVOS);
        }
        // 组装结果
        CQueryResult<NewProductPublishConfigVO> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(Lists.newArrayList());
        return result;
    }

    @Override
    public void updateNewProductPublishConfig(List<NewProductPublishConfig> requestParam) {
        String username = WebUtils.getUserName();
        requestParam.stream().filter(item -> Objects.nonNull(item.getId())).forEach(item -> {
            item.setUpdateBy(username);
            item.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            newProductPublishConfigMapper.updateByPrimaryKeySelective(item);
        });
    }

    /**
     *  获取所有必刊登新品主管人员分配配置
      * @return
     */
    @Override
    public List<NewProductPublishConfig> getAllNewProductPublishConfig() {
        NewProductPublishConfigExample newProductPublishConfigExample = new NewProductPublishConfigExample();
        return newProductPublishConfigMapper.selectByExample(newProductPublishConfigExample);
    }


    @Override
    public ApiResult<String> importSaleConfig(List<NewProductPublishConfigVO> employeeList) {
        String userName = WebUtils.getUserName();
        List<NewProductPublishConfig> allNewProductPublishConfig = getAllNewProductPublishConfig();
        Map<String, NewProductPublishConfig> exitConfig = allNewProductPublishConfig.stream().collect(Collectors.toMap(NewProductPublishConfig::getSaleNumber, Function.identity(), (a, b) -> a));

        List<NewUser> allInUseEmpl = NewUsermgtUtils.getInUseEmplByPlatform(SaleChannel.CHANNEL_AMAZON);
        if (CollectionUtils.isEmpty(allInUseEmpl)) {
            return ApiResult.newError("未查询到在职员工信息");
        }


        Map<String, NewUser> employeeMap = allInUseEmpl.stream().collect(Collectors.toMap(NewUser::getEmployeeNo, Function.identity(), (a, b) -> a));

        List<NewProductPublishConfig> addConfigList = employeeList.stream()
                .filter(employee -> employeeMap.containsKey(employee.getSaleNumber()))
                .filter(employee -> !exitConfig.containsKey(employee.getSaleNumber()))
                .map(employee -> {
                    NewProductPublishConfig publishConfig = new NewProductPublishConfig();
                    publishConfig.setSaleNumber(employee.getSaleNumber());
                    publishConfig.setAllocatedQuantity(employee.getAllocatedQuantity());
                    publishConfig.setCreateBy(userName);
                    publishConfig.setCreateTime(new Timestamp(System.currentTimeMillis()));
                    publishConfig.setUpdateBy(userName);
                    publishConfig.setUpdateTime(new Timestamp(System.currentTimeMillis()));
                    return publishConfig;
                }).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(addConfigList)) {
            newProductPublishConfigMapper.batchInsert(addConfigList);
        }

        List<String> exitAccountNumbers = employeeList.stream()
                .map(NewProductPublishConfigVO::getSaleNumber)
                .filter(exitConfig::containsKey)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(exitAccountNumbers)) {
            return ApiResult.newError(String.format("成功数量:%S,重复添加：%s", addConfigList.size(), String.join(",", exitAccountNumbers)));
        }
        return ApiResult.newSuccess("导入完成");
    }
}