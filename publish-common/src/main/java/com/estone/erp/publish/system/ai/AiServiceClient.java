package com.estone.erp.publish.system.ai;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.common.Constant;
import com.estone.erp.publish.system.ai.bean.ChatCompletionResponse;
import com.estone.erp.publish.system.ai.bean.ChatOpenaiRequest;
import com.estone.erp.publish.system.hystrix.AiServiceClientFallBack;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2023/4/25 17:23
 * @description ai接口服务
 */
@Component
@FeignClient(name = "ai-service", fallbackFactory = AiServiceClientFallBack.class)
public interface AiServiceClient {

    /**
     * openai聊天接口-长轮询
     * http://************/web/#/86/8107
     *
     * @param chatOpenaiRequest
     * @return
     */
    @PostMapping(value = "/openai/chat-process", headers = {"Authorization=" + Constant.productAuth})
    String sendProcess(@RequestBody ChatOpenaiRequest chatOpenaiRequest);

    /**
     * openai聊天接口-普通模式
     * http://************/web/#/86/8108
     *
     * @param chatOpenaiRequest
     * @return
     */
    @PostMapping(value = "/openai/chat-process-ordinary", headers = {"Authorization=" + Constant.productAuth})
    ApiResult<ChatCompletionResponse> sendProcessOrdinary(@RequestBody ChatOpenaiRequest chatOpenaiRequest);

    /**
     * 腾讯deepseek 聊天接口，普通模式
     * http://************/web/#/86/11746
     *
     * @param chatOpenaiRequest
     * @return
     */
    @PostMapping(value = "/tencent/chat/chat-process-ordinary", headers = {"Authorization=" + Constant.productAuth})
    ApiResult<ChatCompletionResponse> sendProcessOrdinaryTencent(@RequestBody ChatOpenaiRequest chatOpenaiRequest);

}
